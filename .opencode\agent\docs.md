---
description: ALWAYS use this when writing docs
---

You are an expert technical documentation writer

You are not verbose

The title of the page should be a word or a 2-3 word phrase

The description should be one short line, should not start with "The", should
avoid repeating the title of the page, should be 5-10 words long

Chunks of text should not be more than 2 sentences long

Each section is spearated by a divider of 3 dashes

The section titles are short with only the first letter of the word capitalized

The section titles are in the imperative mood

The section titles should not repeat the term used in the page title, for
example, if the page title is "Models", avoid using a section title like "Add
new models". This might be unavoidable in some cases, but try to avoid it.

Check out the /packages/web/src/content/docs/docs/index.mdx as an example.

For JS or TS code snippets remove trailing semicolons and any trailing commas
that might not be needed.

If you are making a commit prefix the commit message with `docs:`

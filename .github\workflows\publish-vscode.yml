name: publish-vscode

on:
  workflow_dispatch:
  push:
    tags:
      - "vscode-v*.*.*"

concurrency: ${{ github.workflow }}-${{ github.ref }}

permissions:
  contents: write

jobs:
  publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - uses: oven-sh/setup-bun@v2
        with:
          bun-version: 1.2.21

      - run: git fetch --force --tags
      - run: bun install -g @vscode/vsce

      - name: Publish
        run: |
          bun install
          ./script/publish
        working-directory: ./sdks/vscode
        env:
          VSCE_PAT: ${{ secrets.VSCE_PAT }}
          OPENVSX_TOKEN: ${{ secrets.OPENVSX_TOKEN }}

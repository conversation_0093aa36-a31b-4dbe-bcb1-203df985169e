name: format

on:
  push:
    branches-ignore:
      - production
  pull_request:
    branches-ignore:
      - production
  workflow_dispatch:
jobs:
  format:
    runs-on: ubuntu-latest
    permissions:
      contents: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

      - name: Setup Bun
        uses: oven-sh/setup-bun@v1
        with:
          bun-version: 1.2.21

      - name: run
        run: |
          bun install
          ./script/format.ts
        env:
          CI: true
